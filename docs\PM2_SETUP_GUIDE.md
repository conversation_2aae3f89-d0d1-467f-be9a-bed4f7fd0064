# 🔧 PM2 Setup and Management Guide

This guide covers PM2 installation, configuration, and management for the ToolCrush deployment on Hostinger VPS.

## 📋 Overview

PM2 (Process Manager 2) is a production process manager for Node.js applications that provides:
- Process clustering and load balancing
- Automatic application restarts
- Log management and rotation
- Memory and CPU monitoring
- Zero-downtime deployments

## 🚀 Quick Installation

### Automatic Installation (Recommended)

The VPS setup script automatically installs PM2:

```bash
# Run on your VPS as root
curl -fsSL https://raw.githubusercontent.com/MuhammadShahbaz195/ToolCrush/main/scripts/setup-vps.sh | bash
```

### Manual Installation

If you need to install PM2 manually:

```bash
# Install Node.js 20 (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs

# Install PM2 globally
npm install -g pm2

# Install PM2 log rotation module
pm2 install pm2-logrotate

# Configure PM2 to start on system boot
pm2 startup systemd

# Save current PM2 configuration
pm2 save
```

## ⚙️ Configuration

### PM2 Ecosystem Configuration

The project includes a pre-configured `ecosystem.config.js` file:

```javascript
module.exports = {
  apps: [{
    name: 'toolrapter',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/toolrapter',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // Performance optimizations
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    // Logging
    log_file: '/var/log/pm2/toolrapter.log',
    out_file: '/var/log/pm2/toolrapter-out.log',
    error_file: '/var/log/pm2/toolrapter-error.log',
    // Restart policies
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

### Starting the Application

```bash
# Start using ecosystem file
pm2 start ecosystem.config.js

# Or start manually
pm2 start npm --name "toolrapter" -- start

# Save configuration
pm2 save
```

## 🔍 Monitoring and Management

### Basic Commands

```bash
# Check application status
pm2 status

# View logs (all applications)
pm2 logs

# View logs for specific app
pm2 logs toolrapter

# Monitor in real-time
pm2 monit

# Restart application
pm2 restart toolrapter

# Stop application
pm2 stop toolrapter

# Delete application from PM2
pm2 delete toolrapter

# Reload application (zero-downtime)
pm2 reload toolrapter
```

### Advanced Monitoring

```bash
# Show detailed information
pm2 show toolrapter

# Display memory usage
pm2 list --sort memory

# Display CPU usage
pm2 list --sort cpu

# View process tree
pm2 prettylist

# Generate startup script
pm2 startup

# Save current process list
pm2 save

# Resurrect saved processes
pm2 resurrect
```

## 📊 Performance Optimization

### Cluster Mode Configuration

```javascript
// In ecosystem.config.js
{
  instances: 'max',        // Use all CPU cores
  exec_mode: 'cluster',    // Enable cluster mode
  max_memory_restart: '1G' // Restart if memory exceeds 1GB
}
```

### Memory Management

```bash
# Monitor memory usage
pm2 monit

# Set memory limit
pm2 start app.js --max-memory-restart 1024M

# View memory statistics
pm2 show toolrapter
```

### Log Management

```bash
# Rotate logs manually
pm2 reloadLogs

# Configure log rotation (already done in setup script)
pm2 install pm2-logrotate

# View log rotation settings
pm2 conf pm2-logrotate
```

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. PM2 Not Found

```bash
# Check if PM2 is installed
which pm2

# If not found, install globally
npm install -g pm2

# Add to PATH if needed
export PATH=$PATH:/usr/local/bin
```

#### 2. Application Won't Start

```bash
# Check PM2 logs
pm2 logs toolrapter

# Check application directory
ls -la /var/www/toolrapter

# Verify Node.js version
node --version

# Check dependencies
cd /var/www/toolrapter && npm list
```

#### 3. High Memory Usage

```bash
# Check memory usage
pm2 monit

# Restart application
pm2 restart toolrapter

# Reduce instances if needed
pm2 scale toolrapter 2
```

#### 4. Application Keeps Restarting

```bash
# Check error logs
pm2 logs toolrapter --err

# Increase restart delay
pm2 restart toolrapter --restart-delay=10000

# Check for port conflicts
netstat -tulpn | grep :3000
```

### Health Checks

```bash
# Test application health
curl http://localhost:3000/api/health

# Check if PM2 is managing the process
pm2 list

# Verify process is listening on correct port
netstat -tulpn | grep :3000

# Check system resources
htop
```

## 🔄 Deployment Integration

### GitHub Actions Integration

The deployment pipeline automatically:
1. Stops the current PM2 process
2. Deploys new code
3. Installs dependencies
4. Starts PM2 with new code

```bash
# Manual deployment steps
pm2 stop toolrapter
cd /var/www/toolrapter
git pull origin main
npm install --production
pm2 start toolrapter
```

### Zero-Downtime Deployment

```bash
# Use reload for zero-downtime updates
pm2 reload toolrapter

# Or use graceful reload
pm2 gracefulReload toolrapter
```

## 📈 Monitoring and Alerts

### Built-in Monitoring

```bash
# Real-time monitoring
pm2 monit

# Web-based monitoring (optional)
pm2 web
```

### Log Analysis

```bash
# View recent logs
pm2 logs --lines 100

# Follow logs in real-time
pm2 logs --follow

# Search logs
pm2 logs | grep "ERROR"
```

## 🔧 Maintenance

### Regular Maintenance Tasks

```bash
# Weekly log rotation
pm2 reloadLogs

# Monthly process restart
pm2 restart all

# Check for PM2 updates
npm update -g pm2

# Backup PM2 configuration
pm2 save
```

### Performance Tuning

```bash
# Optimize for production
pm2 start ecosystem.config.js --env production

# Scale based on load
pm2 scale toolrapter +2  # Add 2 instances
pm2 scale toolrapter 4   # Set to exactly 4 instances
```

## 📞 Support

### Getting Help

1. **Check PM2 logs**: `pm2 logs toolrapter`
2. **Monitor resources**: `pm2 monit`
3. **Verify configuration**: `pm2 show toolrapter`
4. **Test manually**: `cd /var/www/toolrapter && npm start`

### Useful Resources

- [PM2 Official Documentation](https://pm2.keymetrics.io/docs/)
- [PM2 Cluster Mode](https://pm2.keymetrics.io/docs/usage/cluster-mode/)
- [PM2 Log Management](https://pm2.keymetrics.io/docs/usage/log-management/)

---

**⚠️ Important**: Always test PM2 configuration changes in a staging environment before applying to production.
