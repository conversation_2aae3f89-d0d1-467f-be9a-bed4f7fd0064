'use client';

import React from "react";
import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import { OptimizedBlogCard } from "./OptimizedBlogCard";

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  categoryName?: string;
  category?: string;
  publishedAt?: string;
  createdAt?: string;
  author: {
    name: string;
    email?: string;
  } | string;
  credit?: string;
}

interface GridLayoutProps {
  posts: BlogPost[];
  loading?: boolean;
  showAnimation?: boolean;
  className?: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { 
    opacity: 0, 
    y: 30, 
    scale: 0.95 
  },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

export const GridLayout = React.memo(function GridLayout({
  posts,
  loading = false,
  showAnimation = true,
  className = ""
}: GridLayoutProps) {
  const [mounted, setMounted] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = true; // Simplified for compatibility

  useEffect(() => {
    setMounted(true);
  }, []);

  // Animation trigger effect
  useEffect(() => {
    // Animation will trigger when component comes into view
  }, [isInView, showAnimation, posts.length]);

  if (!mounted) {
    return <div className="h-96 animate-pulse bg-muted rounded-lg" />;
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 9 }).map((_, index) => (
          <div key={index} className="space-y-4">
            <div className="h-52 bg-muted animate-pulse rounded-t-2xl" />
            <div className="p-6 space-y-3">
              <div className="h-4 bg-muted animate-pulse rounded" />
              <div className="h-6 bg-muted animate-pulse rounded" />
              <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
              <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground text-lg">No blog posts found.</p>
      </div>
    );
  }

  return (
    <motion.div
      ref={containerRef}
      variants={showAnimation ? containerVariants : undefined}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      className={`w-full ${className}`}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {posts.map((post, index) => (
          <motion.div
            key={post.id || post._id || index}
            variants={showAnimation ? itemVariants : undefined}
            className="flex justify-center"
          >
            <OptimizedBlogCard
              post={post}
              index={index}
              showAnimation={false} // We handle animation at container level
              className="w-full max-w-[350px]"
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
});
