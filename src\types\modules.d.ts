// Module declarations for external packages used in ToolCrush

declare module 'framer-motion' {
  import { ComponentType, ReactNode, CSSProperties } from 'react';

  export interface MotionProps {
    initial?: any;
    animate?: any;
    exit?: any;
    whileHover?: any;
    whileTap?: any;
    whileInView?: any;
    viewport?: any;
    transition?: any;
    className?: string;
    style?: CSSProperties;
    children?: ReactNode;
    onClick?: (event?: any) => void;
    onHoverStart?: () => void;
    onHoverEnd?: () => void;
    [key: string]: any;
  }

  export const motion: {
    div: ComponentType<MotionProps>;
    section: ComponentType<MotionProps>;
    article: ComponentType<MotionProps>;
    header: ComponentType<MotionProps>;
    main: ComponentType<MotionProps>;
    footer: ComponentType<MotionProps>;
    nav: ComponentType<MotionProps>;
    aside: ComponentType<MotionProps>;
    h1: ComponentType<MotionProps>;
    h2: ComponentType<MotionProps>;
    h3: ComponentType<MotionProps>;
    h4: ComponentType<MotionProps>;
    h5: ComponentType<MotionProps>;
    h6: ComponentType<MotionProps>;
    p: ComponentType<MotionProps>;
    span: ComponentType<MotionProps>;
    a: ComponentType<MotionProps>;
    button: ComponentType<MotionProps>;
    img: ComponentType<MotionProps>;
    ul: ComponentType<MotionProps>;
    li: ComponentType<MotionProps>;
    form: ComponentType<MotionProps>;
    input: ComponentType<MotionProps>;
    textarea: ComponentType<MotionProps>;
    [key: string]: ComponentType<MotionProps>;
  };

  export interface AnimatePresenceProps {
    children?: ReactNode;
    initial?: boolean;
    exitBeforeEnter?: boolean;
    mode?: 'wait' | 'sync' | 'popLayout';
  }

  export const AnimatePresence: ComponentType<AnimatePresenceProps>;
}

// Lucide React types are now handled by the package itself
// Removed custom declaration to avoid conflicts with actual package types

declare module 'next/link' {
  import { ComponentType, ReactNode, AnchorHTMLAttributes } from 'react';

  export interface LinkProps extends Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    href: string;
    as?: string;
    replace?: boolean;
    scroll?: boolean;
    shallow?: boolean;
    passHref?: boolean;
    prefetch?: boolean;
    locale?: string | false;
    children: ReactNode;
    legacyBehavior?: boolean;
  }

  const Link: ComponentType<LinkProps>;
  export default Link;
}